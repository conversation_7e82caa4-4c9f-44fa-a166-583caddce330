ESX = exports["es_extended"]:getSharedObject()

-- 全局变量
local activeDrugs = {}      -- 活跃的迷药效果
local activeRescues = {}    -- 活跃的求救
local surgeryInProgress = {} -- 进行中的手术

-- 玩家连接时初始化
AddEventHandler('esx:playerLoaded', function(playerId, xPlayer)
    local identifier = xPlayer.identifier

    -- 初始化玩家器官状态
    CreateThread(function()
        Wait(2000) -- 等待数据库准备就绪
        GetPlayerOrgans(identifier)
    end)
end)

-- 调试命令：检查器官状态
RegisterCommand('checkorgans', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    local identifier = xPlayer.identifier
    local organs = GetPlayerOrgans(identifier)

    print(string.format('^2[器官调试] ^7玩家 %s (%s) 的器官状态:', xPlayer.getName(), identifier))
    for organField, status in pairs(organs) do
        if organField ~= 'id' and organField ~= 'identifier' and organField ~= 'last_updated' then
            local statusText = status == 1 and '健康' or '已摘除'
            print(string.format('  %s: %s (%d)', organField, statusText, status))
        end
    end

    -- 发送给客户端显示
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        multiline = true,
        args = {"器官状态", json.encode(organs, {indent = true})}
    })
end, false)

-- 调试命令：重置器官状态
RegisterCommand('resetorgans', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    local identifier = xPlayer.identifier

    -- 删除现有记录
    MySQL.query('DELETE FROM player_organs WHERE identifier = ?', {identifier})

    -- 重新获取（会创建默认状态）
    local organs = GetPlayerOrgans(identifier)

    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        multiline = false,
        args = {"器官系统", "器官状态已重置为默认值"}
    })

    print(string.format('^2[器官调试] ^7玩家 %s 的器官状态已重置', xPlayer.getName()))
end, false)

-- 调试命令：摘除器官（用于测试修复功能）
RegisterCommand('removeorgan', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    if not args[1] then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = false,
            args = {"错误", "用法: /removeorgan <器官名称>"}
        })
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 0},
            multiline = false,
            args = {"提示", "可用器官: organ_heart, organ_liver, organ_kidney, organ_lung, organ_pancreas, organ_spleen"}
        })
        return
    end

    local organField = args[1]
    local validOrgans = {
        'organ_heart', 'organ_liver', 'organ_kidney',
        'organ_lung', 'organ_pancreas', 'organ_spleen'
    }

    local isValid = false
    for _, validOrgan in ipairs(validOrgans) do
        if organField == validOrgan then
            isValid = true
            break
        end
    end

    if not isValid then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = false,
            args = {"错误", "无效的器官名称"}
        })
        return
    end

    local identifier = xPlayer.identifier

    -- 摘除器官（设置为0）
    local success = MySQL.Sync.execute('UPDATE player_organs SET ' .. organField .. ' = 0 WHERE identifier = ?', {
        identifier
    })

    if success then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 165, 0},
            multiline = false,
            args = {"器官系统", string.format("已摘除 %s（用于测试）", organField)}
        })
        print(string.format('^3[器官调试] ^7玩家 %s 的 %s 已被摘除', xPlayer.getName(), organField))
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = false,
            args = {"错误", "摘除器官失败"}
        })
    end
end, false)

-- 修复布尔值问题的命令
RegisterCommand('fixbooleanorgans', function(source, args, rawCommand)
    if source ~= 0 then -- 只允许控制台执行
        return
    end

    print('^3[器官修复] ^7开始修复布尔值问题...')

    -- 修复现有数据的布尔值问题
    MySQL.query([[
        UPDATE player_organs SET
            organ_heart = CASE WHEN organ_heart = true OR organ_heart = 1 OR organ_heart = '1' THEN 1 ELSE 0 END,
            organ_liver = CASE WHEN organ_liver = true OR organ_liver = 1 OR organ_liver = '1' THEN 1 ELSE 0 END,
            organ_kidney = CASE WHEN organ_kidney = true OR organ_kidney = 1 OR organ_kidney = '1' THEN 1 ELSE 0 END,
            organ_lung = CASE WHEN organ_lung = true OR organ_lung = 1 OR organ_lung = '1' THEN 1 ELSE 0 END,
            organ_pancreas = CASE WHEN organ_pancreas = true OR organ_pancreas = 1 OR organ_pancreas = '1' THEN 1 ELSE 0 END,
            organ_spleen = CASE WHEN organ_spleen = true OR organ_spleen = 1 OR organ_spleen = '1' THEN 1 ELSE 0 END
    ]], {}, function(result)
        if result then
            print('^2[器官修复] ^7布尔值修复完成')
        else
            print('^1[器官修复] ^7布尔值修复失败')
        end
    end)
end, true)

-- 测试器官状态刷新的命令
RegisterCommand('testrefresh', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    local targetId = tonumber(args[1]) or source
    local organToRemove = args[2] or 'organ_heart'

    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)

    -- 模拟摘除器官
    UpdatePlayerOrgan(targetIdentifier, organToRemove, 0)

    -- 获取更新后的状态
    local updatedOrgans = GetPlayerOrgans(targetIdentifier)
    local organStatusOnly = {
        organ_heart = updatedOrgans.organ_heart,
        organ_liver = updatedOrgans.organ_liver,
        organ_kidney = updatedOrgans.organ_kidney,
        organ_lung = updatedOrgans.organ_lung,
        organ_pancreas = updatedOrgans.organ_pancreas,
        organ_spleen = updatedOrgans.organ_spleen
    }

    -- 发送刷新消息
    TriggerClientEvent('organ_trade:refreshOrganUI', source, organStatusOnly)

    TriggerClientEvent('chat:addMessage', source, {
        color = {255, 165, 0},
        multiline = false,
        args = {"测试系统", string.format("已模拟摘除 %s，UI应该已刷新", organToRemove)}
    })

    print(string.format('^3[测试刷新] ^7玩家 %s 模拟摘除 %s', xPlayer.getName(), organToRemove))
end, false)

-- 玩家断开连接时清理
AddEventHandler('esx:playerDropped', function(playerId, reason)
    local identifier = GetPlayerIdentifier(playerId, 0)
    
    -- 清理相关数据
    if activeDrugs[identifier] then
        activeDrugs[identifier] = nil
    end
    
    if surgeryInProgress[identifier] then
        surgeryInProgress[identifier] = nil
    end
    
    -- 清理求救记录
    for callId, rescue in pairs(activeRescues) do
        if rescue.victim == identifier then
            activeRescues[callId] = nil
            break
        end
    end
end)

-- 获取玩家标识符
function GetPlayerIdentifierByServerId(serverId)
    return GetPlayerIdentifier(serverId, 0)
end

-- 检查玩家权限
function HasPermission(playerId, permissionType)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if not xPlayer then return false end

    local permissions = Config.Permissions[permissionType]
    if not permissions then return false end

    -- 如果权限列表为空，表示所有玩家都有权限
    if #permissions == 0 then
        return true
    end

    for _, permission in ipairs(permissions) do
        if xPlayer.job.name == permission then
            return true
        end

        -- 检查帮派权限（如果有帮派系统）
        if permission == 'gang' and xPlayer.gang then
            return true
        end
    end

    return false
end

-- 发送通知给玩家
function NotifyPlayer(playerId, message, type)
    type = type or 'info'

    -- 调试信息
    DebugPrint(string.format('发送通知给玩家 %d: %s (类型: %s)', playerId, message, type), 'rescue')

    -- 尝试多种通知方式以确保兼容性
    local success = false

    -- 方式1: ESX标准通知
    pcall(function()
        TriggerClientEvent('esx:showNotification', playerId, message, type)
        success = true
    end)

    -- 方式2: ESX新版本通知
    if not success then
        pcall(function()
            TriggerClientEvent('esx:showNotification', playerId, message)
            success = true
        end)
    end

    -- 方式3: 聊天消息作为备用
    if not success then
        pcall(function()
            TriggerClientEvent('chat:addMessage', playerId, {
                color = type == 'error' and {255, 0, 0} or type == 'success' and {0, 255, 0} or {255, 255, 255},
                multiline = false,
                args = {"系统", message}
            })
            success = true
        end)
    end

    if not success then
        DebugPrint(string.format('警告: 无法向玩家 %d 发送通知', playerId), 'rescue')
    end
end

-- 发送通知给所有指定职业的玩家
function NotifyJob(jobs, message, type)
    local xPlayers = ESX.GetExtendedPlayers()
    local notifiedCount = 0

    -- 调试：记录NotifyJob调用
    print(string.format('^3[NotifyJob] ^7开始通知职业: %s, 消息: %s', json.encode(jobs), message))
    print(string.format('^3[NotifyJob] ^7在线玩家总数: %d', #xPlayers))

    for _, xPlayer in pairs(xPlayers) do
        local playerJob = xPlayer.job and xPlayer.job.name or 'unknown'

        for _, job in ipairs(jobs) do
            if xPlayer.job and xPlayer.job.name == job then
                NotifyPlayer(xPlayer.source, message, type)
                notifiedCount = notifiedCount + 1
                print(string.format('^2[NotifyJob] ^7已通知 %s (ID: %d, 职业: %s)', xPlayer.getName(), xPlayer.source, job))
                break
            end
        end
    end

    print(string.format('^3[NotifyJob] ^7通知完成，成功通知 %d 名玩家', notifiedCount))

    if notifiedCount == 0 then
        print('^1[NotifyJob] ^7警告: 没有找到任何符合条件的玩家！')
    end
end

-- 获取玩家坐标
function GetPlayerCoords(playerId)
    local ped = GetPlayerPed(playerId)
    return GetEntityCoords(ped)
end

-- 计算两点距离
function GetDistance(pos1, pos2)
    return #(vector3(pos1.x, pos1.y, pos1.z) - vector3(pos2.x, pos2.y, pos2.z))
end

-- 检查玩家是否在指定位置附近
function IsPlayerNearLocation(playerId, locations, maxDistance)
    local playerCoords = GetPlayerCoords(playerId)
    maxDistance = maxDistance or 5.0
    
    for _, location in ipairs(locations) do
        local distance = GetDistance(playerCoords, location)
        if distance <= maxDistance then
            return true, location
        end
    end
    
    return false
end

-- 给玩家添加道具
function GivePlayerItem(playerId, item, count, metadata)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if xPlayer then
        xPlayer.addInventoryItem(item, count or 1, metadata)
        return true
    end
    return false
end

-- 从玩家移除道具
function RemovePlayerItem(playerId, item, count)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if xPlayer then
        xPlayer.removeInventoryItem(item, count or 1)
        return true
    end
    return false
end

-- 检查玩家是否有道具
function HasPlayerItem(playerId, item, count)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if xPlayer then
        local itemCount = xPlayer.getInventoryItem(item).count
        return itemCount >= (count or 1)
    end
    return false
end

-- 获取在线玩家列表（排除自己）
function GetOnlinePlayersExcept(excludeId)
    local players = {}
    local allPlayers = ESX.GetExtendedPlayers()
    
    for _, xPlayer in pairs(allPlayers) do
        if xPlayer.source ~= excludeId then
            table.insert(players, {
                id = xPlayer.source,
                name = xPlayer.getName(),
                identifier = xPlayer.identifier
            })
        end
    end
    
    return players
end

-- 调试打印函数
function DebugPrint(message, category)
    if Config.Debug or (category == 'rescue' and Config.DebugRescue) then
        local prefix = category and string.format('^3[%s] ^7', category:upper()) or '^3[DEBUG] ^7'
        print(prefix .. message)
    end
end

-- 日志记录
function LogAction(action, playerId, targetId, details)
    local timestamp = os.date('%Y-%m-%d %H:%M:%S')
    local playerName = GetPlayerName(playerId) or 'Unknown'
    local targetName = targetId and GetPlayerName(targetId) or 'None'

    local logMessage = string.format('[%s] %s | Player: %s (%d) | Target: %s (%s) | Details: %s',
        timestamp, action, playerName, playerId, targetName, targetId or 'None', details or 'None')

    print(logMessage)

    -- 这里可以添加文件日志或数据库日志
end

-- 导出函数供其他模块使用
exports('GetPlayerIdentifierByServerId', GetPlayerIdentifierByServerId)
exports('HasPermission', HasPermission)
exports('NotifyPlayer', NotifyPlayer)
exports('NotifyJob', NotifyJob)
exports('GetPlayerCoords', GetPlayerCoords)
exports('GetDistance', GetDistance)
exports('IsPlayerNearLocation', IsPlayerNearLocation)
exports('GivePlayerItem', GivePlayerItem)
exports('RemovePlayerItem', RemovePlayerItem)
exports('HasPlayerItem', HasPlayerItem)
exports('GetOnlinePlayersExcept', GetOnlinePlayersExcept)
exports('LogAction', LogAction)

-- 全局变量导出
_G.activeDrugs = activeDrugs
_G.activeRescues = activeRescues
_G.surgeryInProgress = surgeryInProgress

print('^2[器官交易系统] ^7主服务器模块已加载')
