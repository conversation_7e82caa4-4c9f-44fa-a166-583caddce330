-- 医疗救治系统客户端

-- 传送到医院
RegisterNetEvent('organ_trade:teleportToHospital', function(hospital)
    local playerPed = PlayerPedId()
    SetEntityCoords(playerPed, hospital.x, hospital.y, hospital.z, false, false, false, true)
    
    -- 淡入淡出效果
    DoScreenFadeOut(1000)
    Wait(1000)
    DoScreenFadeIn(1000)
end)



-- 使用手术台
function UseSurgeryTable()
    -- 检查是否在手术台附近
    TriggerServerEvent('organ_trade:checkSurgeryTableLocation')
end

-- 接收附近患者列表（用于手术台）
RegisterNetEvent('organ_trade:receiveSurgeryTablePatients', function(patients)
    local options = {}

    -- 添加自我治疗选项
    table.insert(options, {
        title = '自我检查',
        description = '检查自己的器官状态',
        onSelect = function()
            TriggerServerEvent('organ_trade:openSurgeryTableUI', nil)
        end
    })

    -- 添加其他患者
    for _, patient in ipairs(patients) do
        table.insert(options, {
            title = string.format('%s (距离: %.1fm)', patient.name, patient.distance),
            description = '检查此患者的器官状态',
            onSelect = function()
                TriggerServerEvent('organ_trade:openSurgeryTableUI', patient.id)
            end
        })
    end

    if #options == 1 then
        -- 只有自我治疗选项
        TriggerServerEvent('organ_trade:openSurgeryTableUI', nil)
        return
    end

    lib.registerContext({
        id = 'surgery_table_patient_menu',
        title = '选择患者',
        options = options
    })

    lib.showContext('surgery_table_patient_menu')
end)

-- 接收附近患者列表（用于肾上腺素）
RegisterNetEvent('organ_trade:receiveNearbyPatients', function(patients)
    local options = {}

    for _, patient in ipairs(patients) do
        table.insert(options, {
            title = string.format('%s (距离: %.1fm)', patient.name, patient.distance),
            description = '为此患者注射肾上腺素',
            onSelect = function()
                TriggerServerEvent('organ_trade:useAdrenaline', patient.id)
            end
        })
    end

    if #options == 0 then
        lib.notify({
            title = '医护系统',
            description = '附近没有需要治疗的患者',
            type = 'error'
        })
        return
    end

    lib.registerContext({
        id = 'adrenaline_patient_menu',
        title = '选择患者',
        options = options
    })

    lib.showContext('adrenaline_patient_menu')
end)

-- 打开手术台UI
RegisterNetEvent('organ_trade:openSurgeryTableUI', function(targetId, organStatus)
    -- 使用和解剖一样的UI，但模式不同
    OpenSurgeryTableUI(targetId, organStatus)
end)

-- 开始器官修复动画
RegisterNetEvent('organ_trade:startOrganRepair', function(duration, organName)
    local playerPed = PlayerPedId()

    -- 显示进度条（包含动画）
    lib.progressBar({
        duration = duration,
        label = string.format('正在修复%s...', organName),
        useWhileDead = false,
        canCancel = false,
        disable = {
            car = true,
            move = true,
            combat = true
        },
        anim = {
            dict = 'mini@repair',
            clip = 'fixing_a_ped'
        }
    })

    -- 确保控制在完成后正确恢复
    CreateThread(function()
        Wait(duration + 100) -- 稍微延迟以确保进度条完成

        -- 确保控制已恢复（防止卡住）
        EnableAllControlActions(0)
    end)
end)

-- 刷新手术台UI
RegisterNetEvent('organ_trade:refreshSurgeryTableUI', function(organStatus)
    -- 刷新UI中的器官状态
    SendNUIMessage({
        type = 'refreshOrganStatus',
        organStatus = organStatus
    })
end)

-- 创建医院标记
CreateThread(function()
    for _, hospital in ipairs(Config.Medical.hospital_locations) do
        local blip = AddBlipForCoord(hospital.x, hospital.y, hospital.z)
        SetBlipSprite(blip, 61)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.8)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString(hospital.name)
        EndTextCommandSetBlipName(blip)
    end
end)

-- 创建手术台标记和交互
CreateThread(function()
    for i, surgeryTable in ipairs(Config.Medical.surgery_table) do
        -- 创建标记
        local blip = AddBlipForCoord(surgeryTable.x, surgeryTable.y, surgeryTable.z)
        SetBlipSprite(blip, 403)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.6)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString('手术台')
        EndTextCommandSetBlipName(blip)
        
        -- 创建交互点
        CreateThread(function()
            while true do
                local playerCoords = GetEntityCoords(PlayerPedId())
                local distance = #(playerCoords - vector3(surgeryTable.x, surgeryTable.y, surgeryTable.z))
                
                if distance <= 3.0 then
                    if IsControlJustPressed(0, 38) then -- E键
                        UseSurgeryTable()
                    end
                end
                
                Wait(distance <= 10.0 and 100 or 1000)
            end
        end)
    end
end)

-- 医护人员命令
RegisterCommand('surgery', function()
    UseSurgeryTable()
end)

-- 添加建议文本
TriggerEvent('chat:addSuggestion', '/surgery', '使用手术台（医护人员）')

-- 导出函数
exports('UseSurgeryTable', UseSurgeryTable)
exports('IsLifeExtended', function() return lifeExtended end)

print('^2[器官交易系统] ^7医疗救治系统客户端已加载')
