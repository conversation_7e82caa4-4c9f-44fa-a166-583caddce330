-- UI界面处理

local uiOpen = false
local currentMenu = nil
local currentTargetId = nil

-- 打开人体器官选择UI（解剖模式）
function OpenOrganSelectionUI(targetId, organStatus)
    if uiOpen then return end

    uiOpen = true
    currentTargetId = targetId
    SetNuiFocus(true, true)

    SendNUIMessage({
        type = 'openOrganSelection',
        targetId = targetId,
        organs = Config.Surgery.organs,
        organStatus = organStatus,
        mode = 'extraction' -- 解剖模式
    })
end

-- 打开手术台UI（修复模式）
function OpenSurgeryTableUI(targetId, organStatus)
    if uiOpen then return end

    uiOpen = true
    currentTargetId = targetId
    SetNuiFocus(true, true)

    SendNUIMessage({
        type = 'openOrganSelection',
        targetId = targetId,
        organs = Config.Surgery.organs,
        organStatus = organStatus,
        mode = 'repair' -- 修复模式
    })
end

-- 关闭UI
function CloseUI()
    if not uiOpen then return end

    uiOpen = false
    SetNuiFocus(false, false)

    -- 如果有正在进行的手术，通知服务端结束手术
    if currentTargetId then
        TriggerServerEvent('organ_trade:endSurgery', currentTargetId)
        currentTargetId = nil
    end

    SendNUIMessage({
        type = 'closeUI'
    })
end

-- NUI回调处理
RegisterNUICallback('selectOrgan', function(data, cb)
    local organKey = data.organ
    local targetId = data.targetId

    if organKey and targetId then
        TriggerServerEvent('organ_trade:extractOrgan', targetId, organKey)
    end

    -- 不关闭UI，保持界面打开以便连续操作
    cb('ok')
end)

RegisterNUICallback('repairOrgan', function(data, cb)
    local organKey = data.organ
    local organField = data.organField
    local targetId = data.targetId

    print(string.format('^3[NUI回调] ^7收到修复器官请求 - 器官: %s, 字段: %s, 目标: %s',
        tostring(organKey), tostring(organField), tostring(targetId)))

    if organKey and organField and targetId then
        TriggerServerEvent('organ_trade:repairOrgan', targetId, organField)
        print('^2[NUI回调] ^7已发送修复器官事件到服务器')
    else
        print('^1[NUI回调] ^7参数不完整，无法发送修复器官事件')
    end

    -- 不关闭UI，保持界面打开以便连续操作
    cb('ok')
end)

RegisterNUICallback('cancelSurgery', function(data, cb)
    local targetId = data.targetId

    if targetId then
        TriggerServerEvent('organ_trade:cancelSurgery', targetId)
    end

    CloseUI()
    cb('ok')
end)

RegisterNUICallback('endSurgery', function(data, cb)
    local targetId = data.targetId

    if targetId then
        TriggerServerEvent('organ_trade:endSurgery', targetId)
    end

    CloseUI()
    cb('ok')
end)



RegisterNUICallback('closeUI', function(data, cb)
    CloseUI()
    cb('ok')
end)

-- 处理确认对话框请求
RegisterNUICallback('showConfirmDialog', function(data, cb)
    local confirmType = data.type
    local title = data.title or '确认'
    local message = data.message or '确认执行此操作？'

    -- 显示ox_lib确认对话框
    local alert = lib.alertDialog({
        header = title,
        content = message,
        centered = true,
        cancel = true
    })

    -- 根据用户选择执行相应操作
    if alert == 'confirm' then
        if confirmType == 'repair' then
            -- 修复器官
            print(string.format('^3[确认对话框] ^7用户确认修复器官 - 器官: %s, 字段: %s, 目标: %s',
                tostring(data.organKey), tostring(data.organField), tostring(data.targetId)))

            if data.organKey and data.organField and data.targetId then
                TriggerServerEvent('organ_trade:repairOrgan', data.targetId, data.organField)
                print('^2[确认对话框] ^7已发送修复器官事件到服务器')
            end

        elseif confirmType == 'extraction' then
            -- 解剖器官
            print(string.format('^3[确认对话框] ^7用户确认解剖器官 - 器官: %s, 目标: %s',
                tostring(data.organKey), tostring(data.targetId)))

            if data.organKey and data.targetId then
                TriggerServerEvent('organ_trade:extractOrgan', data.targetId, data.organKey)
                print('^2[确认对话框] ^7已发送解剖器官事件到服务器')
            end

        elseif confirmType == 'cancelSurgery' then
            -- 取消手术
            print(string.format('^3[确认对话框] ^7用户确认取消手术 - 目标: %s', tostring(data.targetId)))

            if data.targetId then
                TriggerServerEvent('organ_trade:cancelSurgery', data.targetId)
                CloseUI()
                print('^2[确认对话框] ^7已发送取消手术事件到服务器')
            end

        elseif confirmType == 'endSurgery' then
            -- 结束手术
            print(string.format('^3[确认对话框] ^7用户确认结束手术 - 目标: %s', tostring(data.targetId)))

            if data.targetId then
                TriggerServerEvent('organ_trade:endSurgery', data.targetId)
                CloseUI()
                print('^2[确认对话框] ^7已发送结束手术事件到服务器')
            end
        end
    else
        print('^1[确认对话框] ^7用户取消了操作')
    end

    cb('ok')
end)

-- ESC键关闭UI
CreateThread(function()
    while true do
        Wait(0)
        if uiOpen then
            if IsControlJustPressed(0, 322) or IsControlJustReleased(0, 322) or IsDisabledControlJustPressed(0, 322) then
                CloseUI()
            end
        end
    end
end)

-- 显示进度条
function ShowProgressBar(duration, text)
    SendNUIMessage({
        type = 'showProgress',
        duration = duration,
        text = text or '处理中...'
    })
end

-- 隐藏进度条
function HideProgressBar()
    SendNUIMessage({
        type = 'hideProgress'
    })
end

-- 显示通知 (支持HTML UI和ox_lib)
function ShowNotification(message, type, duration)
    -- 使用HTML UI通知
    SendNUIMessage({
        type = 'showNotification',
        message = message,
        notificationType = type or 'info',
        duration = duration or 5000
    })

    -- 同时使用ox_lib通知作为备用
    lib.notify({
        title = '器官交易系统',
        description = message,
        type = type or 'info',
        duration = duration or 5000
    })
end

-- 显示倒计时
function ShowCountdown(duration, text)
    SendNUIMessage({
        type = 'showCountdown',
        duration = duration,
        text = text or '倒计时'
    })
end

-- 隐藏倒计时
function HideCountdown()
    SendNUIMessage({
        type = 'hideCountdown'
    })
end

-- 更新玩家状态显示
function UpdatePlayerStatus(status)
    SendNUIMessage({
        type = 'updateStatus',
        status = status
    })
end

-- 显示器官状态
function ShowOrganStatus(organs)
    SendNUIMessage({
        type = 'showOrganStatus',
        organs = organs
    })
end

-- 刷新器官状态
RegisterNetEvent('organ_trade:refreshOrganStatus', function()
    if currentTargetId then
        print('^3[UI] ^7刷新器官状态，目标ID: ' .. tostring(currentTargetId))
        TriggerServerEvent('organ_trade:getPlayerOrganStatus', currentTargetId)
    end
end)

-- 导出函数
exports('OpenOrganSelectionUI', OpenOrganSelectionUI)
exports('CloseUI', CloseUI)
exports('ShowProgressBar', ShowProgressBar)
exports('HideProgressBar', HideProgressBar)
exports('ShowNotification', ShowNotification)
exports('ShowCountdown', ShowCountdown)
exports('HideCountdown', HideCountdown)
exports('UpdatePlayerStatus', UpdatePlayerStatus)
exports('ShowOrganStatus', ShowOrganStatus)

print('^2[器官交易系统] ^7UI模块已加载')
