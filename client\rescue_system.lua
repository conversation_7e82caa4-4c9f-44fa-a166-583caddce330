-- 求救系统客户端

local canSendSOS = Config.Rescue.allow_all_players or false  -- 根据配置决定是否允许所有玩家发送求救信号

-- 启用SOS功能
RegisterNetEvent('organ_trade:enableSOS', function()
    canSendSOS = true
    lib.notify({
        title = '器官交易系统',
        description = '你现在可以发送求救信号 /sos',
        type = 'info'
    })
end)

-- 启动求救倒计时
RegisterNetEvent('organ_trade:startRescueCountdown', function(duration)
    rescueCountdownActive = true
    local countdownDuration = duration or Config.Rescue.death_countdown
    rescueCountdownEndTime = GetGameTimer() + countdownDuration

    -- 计算倒计时分钟数用于显示
    local minutes = math.floor(countdownDuration / 60000)

    -- 使用HTML UI显示倒计时
    exports['qiguan_tudou']:ShowCountdown(countdownDuration, '⚠️ 生命危险 - 死亡倒计时 ⚠️')

    lib.notify({
        title = '⚠️ 生命危险！',
        description = string.format(Config.Notifications.countdown_started, minutes),
        type = 'error',
        duration = 8000
    })

    print('^1[求救倒计时] ^7倒计时已启动，持续时间: ' .. countdownDuration .. 'ms (' .. minutes .. '分钟)')
end)

-- 停止求救倒计时
RegisterNetEvent('organ_trade:stopRescueCountdown', function()
    rescueCountdownActive = false
    rescueCountdownEndTime = 0

    -- 隐藏HTML UI倒计时
    exports['qiguan_tudou']:HideCountdown()

    lib.notify({
        title = '✅ 获得救治！',
        description = Config.Notifications.countdown_stopped,
        type = 'success',
        duration = 6000
    })

    print('^2[求救倒计时] ^7倒计时已停止')
end)

-- 延长求救倒计时
RegisterNetEvent('organ_trade:extendRescueCountdown', function(extendTime)
    print('^3[倒计时延长] ^7收到延长事件，延长时间: ' .. extendTime .. 'ms')
    print('^3[倒计时延长] ^7当前倒计时状态: ' .. tostring(rescueCountdownActive))

    if rescueCountdownActive then
        local oldEndTime = rescueCountdownEndTime
        -- 延长倒计时结束时间
        rescueCountdownEndTime = rescueCountdownEndTime + extendTime

        print('^3[倒计时延长] ^7原结束时间: ' .. oldEndTime)
        print('^3[倒计时延长] ^7新结束时间: ' .. rescueCountdownEndTime)
        print('^3[倒计时延长] ^7当前时间: ' .. GetGameTimer())

        local minutes = math.floor(extendTime / 60000)

        -- 重新启动HTML UI倒计时以反映新的时间
        local newRemainingTime = rescueCountdownEndTime - GetGameTimer()
        exports['qiguan_tudou']:ShowCountdown(newRemainingTime, '⚠️ 生命危险 - 死亡倒计时 ⚠️')

        lib.notify({
            title = '💉 肾上腺素注射',
            description = string.format('生命倒计时延长了 %d 分钟', minutes),
            type = 'success',
            duration = 5000
        })

        print('^2[求救倒计时] ^7倒计时延长了 ' .. extendTime .. 'ms (' .. minutes .. '分钟)')
    else
        print('^1[倒计时延长] ^7错误：倒计时未激活，无法延长')
    end
end)

-- 发送SOS命令
RegisterCommand(Config.Rescue.command, function()
    if not canSendSOS then
        lib.notify({
            title = '器官交易系统',
            description = '你现在无法发送求救信号',
            type = 'error'
        })
        return
    end

    -- 确认对话框
    local alert = lib.alertDialog({
        header = '发送求救信号',
        content = '确认发送求救信号？',
        centered = true,
        cancel = true
    })

    if alert == 'confirm' then
        TriggerServerEvent('organ_trade:sendSOS')
        canSendSOS = false -- 防止重复发送
    end
end)

-- 当前待响应的求救信号
local pendingRescueCall = nil
local rescueResponseBlip = nil

-- 求救倒计时相关变量
local rescueCountdownActive = false
local rescueCountdownEndTime = 0



-- 接收SOS求救信号（医护人员）
RegisterNetEvent('organ_trade:receiveSOSCall', function(callData)
    -- 调试：记录接收到的求救信号
    print(string.format('^2[客户端求救] ^7收到求救信号 - CallID: %s, 受害者: %s',
        tostring(callData.callId), callData.victimName))
    print(string.format('^2[客户端求救] ^7坐标: %.2f, %.2f, %.2f, 时间: %s',
        callData.coords.x, callData.coords.y, callData.coords.z, callData.timestamp))

    -- 保存求救信息，等待响应
    pendingRescueCall = callData

    -- 显示通知，提示按E响应
    local libSuccess = pcall(function()
        lib.notify({
            title = '🚨 收到求救信号！',
            description = string.format('受害者: %s\n时间: %s\n按 [E] 键响应求救',
                callData.victimName, callData.timestamp),
            type = 'error',
            duration = 15000
        })
    end)

    -- 屏幕提示按E响应
    CreateThread(function()
        local endTime = GetGameTimer() + Config.Rescue.response_timeout -- 使用配置的超时时间
        while GetGameTimer() < endTime and pendingRescueCall do
            local remainingTime = math.ceil((endTime - GetGameTimer()) / 1000)
            SetTextFont(0)
            SetTextProportional(1)
            SetTextScale(0.7, 0.7)
            SetTextColour(255, 255, 0, 255)
            SetTextDropshadow(0, 0, 0, 0, 255)
            SetTextEdge(1, 0, 0, 0, 255)
            SetTextDropShadow()
            SetTextOutline()
            SetTextEntry("STRING")
            AddTextComponentString(string.format("🚨 求救信号\n受害者: %s\n按 [E] 响应 (%ds)", callData.victimName, remainingTime))
            DrawText(0.5, 0.1)
            Wait(0)
        end

        -- 超时处理
        if pendingRescueCall then
            pendingRescueCall = nil
            lib.notify({
                title = '响应超时',
                description = '求救信号响应时间已过',
                type = 'error',
                duration = 5000
            })
        end
    end)

    -- 播放警报声
    PlaySoundFrontend(-1, 'TIMER_STOP', 'HUD_MINI_GAME_SOUNDSET', 1)

    -- 额外的声音提示
    CreateThread(function()
        for i = 1, 3 do
            Wait(1000)
            PlaySoundFrontend(-1, 'BEEP_RED', 'HUD_MINI_GAME_SOUNDSET', 1)
        end
    end)

    print('^2[客户端求救] ^7求救信号接收完成，等待响应')
end)

-- 检测E键响应求救
CreateThread(function()
    while true do
        Wait(0)

        if pendingRescueCall and IsControlJustPressed(0, 38) then -- E键
            -- 响应求救信号
            TriggerServerEvent('organ_trade:respondToSOS', pendingRescueCall.callId)

            -- 清除待响应状态
            pendingRescueCall = nil

            -- 播放确认音效
            PlaySoundFrontend(-1, 'SELECT', 'HUD_FRONTEND_DEFAULT_SOUNDSET', 1)
        end
    end
end)

-- 接收求救响应确认（创建导航标记）
RegisterNetEvent('organ_trade:setRescueWaypoint', function(coords, victimName)
    -- 移除旧的标记和GPS路线
    if rescueResponseBlip and DoesBlipExist(rescueResponseBlip) then
        SetBlipRoute(rescueResponseBlip, false) -- 清除旧的GPS路线
        RemoveBlip(rescueResponseBlip)
        print('^3[客户端求救] ^7已清除旧的救援标记')
    end

    -- 创建导航标记
    rescueResponseBlip = AddBlipForCoord(coords.x, coords.y, coords.z)
    SetBlipSprite(rescueResponseBlip, 1) -- 导航点图标
    SetBlipDisplay(rescueResponseBlip, 4)
    SetBlipScale(rescueResponseBlip, 1.2)
    SetBlipColour(rescueResponseBlip, 2) -- 绿色
    SetBlipAsShortRange(rescueResponseBlip, false)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString(string.format('救援目标 - %s', victimName))
    EndTextCommandSetBlipName(rescueResponseBlip)

    -- 设置GPS导航
    SetBlipRoute(rescueResponseBlip, true)
    SetBlipRouteColour(rescueResponseBlip, 2)

    -- 通知医护人员
    lib.notify({
        title = '✅ 响应成功！',
        description = string.format('正在前往救援 %s\nGPS导航已设置', victimName),
        type = 'success',
        duration = 8000
    })

    -- 开始检测到达目标位置
    CreateThread(function()
        local targetCoords = vector3(coords.x, coords.y, coords.z)
        local arrivalDistance = Config.Rescue.arrival_distance -- 使用配置中的到达距离
        local hasArrived = false
        local lastDistance = 999999

        print(string.format('^3[到达检测] ^7开始检测到达，目标坐标: %.2f, %.2f, %.2f', targetCoords.x, targetCoords.y, targetCoords.z))
        print(string.format('^3[到达检测] ^7到达距离阈值: %.1f米', arrivalDistance))

        while rescueResponseBlip and DoesBlipExist(rescueResponseBlip) and not hasArrived do
            Wait(1000) -- 每秒检查一次

            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local distance = #(playerCoords - targetCoords)

            -- 每5秒打印一次距离信息
            if math.floor(GetGameTimer() / 5000) ~= math.floor((GetGameTimer() - 1000) / 5000) then
                print(string.format('^3[到达检测] ^7当前距离: %.1f米 (阈值: %.1f米)', distance, arrivalDistance))
            end

            -- 显示距离提示（当距离小于50米时）
            if distance <= 50.0 and distance > arrivalDistance then
                -- 每5米显示一次距离提示
                if math.floor(distance / 5) ~= math.floor(lastDistance / 5) then
                    lib.notify({
                        title = '🚗 接近目标',
                        description = string.format('距离救援点还有 %.0f 米', distance),
                        type = 'info',
                        duration = 3000
                    })
                end
            end

            lastDistance = distance

            if distance <= arrivalDistance then
                hasArrived = true

                -- 清除导航标记和GPS路线
                if DoesBlipExist(rescueResponseBlip) then
                    SetBlipRoute(rescueResponseBlip, false) -- 清除GPS路线
                    RemoveBlip(rescueResponseBlip)
                    rescueResponseBlip = nil
                end

                -- 通知医护人员已到达
                lib.notify({
                    title = '🎯 已到达救援点！',
                    description = string.format('你已到达 %s 的位置\n开始救援行动', victimName),
                    type = 'success',
                    duration = 8000
                })

                -- 播放到达音效
                PlaySoundFrontend(-1, 'WAYPOINT_SET', 'HUD_FRONTEND_DEFAULT_SOUNDSET', 1)

                -- 通知服务器医护已到达
                TriggerServerEvent('organ_trade:medicArrived', coords, victimName)

                print(string.format('^2[客户端求救] ^7医护人员已到达救援点: %s (距离: %.1f米)，地图标记已清除', victimName, distance))
                break
            end
        end
    end)

    print(string.format('^2[客户端求救] ^7已设置救援导航点: %s', victimName))
end)

-- 清除救援标记
RegisterNetEvent('organ_trade:clearRescueWaypoint', function()
    print('^3[清除标记] ^7收到清除救援标记事件')

    if rescueResponseBlip then
        if DoesBlipExist(rescueResponseBlip) then
            -- 清除GPS路线
            SetBlipRoute(rescueResponseBlip, false)
            -- 移除地图标记
            RemoveBlip(rescueResponseBlip)
            print('^2[清除标记] ^7救援标记已成功移除')
        else
            print('^1[清除标记] ^7警告: 标记ID存在但标记不存在于地图上')
        end
        rescueResponseBlip = nil

        -- 通知玩家标记已清除
        lib.notify({
            title = '✅ 导航清除',
            description = '救援标记已清除',
            type = 'success',
            duration = 3000
        })
    else
        print('^1[清除标记] ^7警告: 没有找到要清除的救援标记')
    end
end)

-- 清除待响应的求救信号（当其他医护已响应时）
RegisterNetEvent('organ_trade:clearPendingRescue', function()
    if pendingRescueCall then
        pendingRescueCall = nil

        lib.notify({
            title = '求救信号已被响应',
            description = '其他医护人员已接受该求救信号',
            type = 'info',
            duration = 5000
        })

        print('^3[客户端求救] ^7待响应的求救信号已清除（其他医护已响应）')
    end
end)

-- 求救倒计时监控（只负责检测倒计时结束，显示由HTML UI处理）
CreateThread(function()
    while true do
        Wait(1000) -- 每秒检查一次

        if rescueCountdownActive then
            local currentTime = GetGameTimer()
            local remainingTime = rescueCountdownEndTime - currentTime

            if remainingTime <= 0 then
                -- 倒计时结束
                rescueCountdownActive = false
                rescueCountdownEndTime = 0

                -- 隐藏HTML UI倒计时
                exports['qiguan_tudou']:HideCountdown()

                -- 通知服务器倒计时结束
                TriggerServerEvent('organ_trade:rescueCountdownExpired')

                print('^1[求救倒计时] ^7倒计时结束')
            end
        end
    end
end)

-- 处理玩家死亡
RegisterNetEvent('organ_trade:handlePlayerDeath', function()
    local playerPed = PlayerPedId()

    -- 设置玩家死亡状态
    SetEntityHealth(playerPed, 0)

    -- 播放死亡音效
    PlaySoundFrontend(-1, 'DEATH', 'HUD_FRONTEND_DEFAULT_SOUNDSET', 1)

    print('^1[死亡处理] ^7玩家因求救倒计时结束而死亡')
end)

-- 查看活跃求救列表（医护人员）
function ViewActiveRescues()
    TriggerServerEvent('organ_trade:getActiveRescues')
end

-- 接收活跃求救列表
RegisterNetEvent('organ_trade:receiveActiveRescues', function(rescues)
    if #rescues == 0 then
        lib.notify({
            title = '器官交易系统',
            description = '当前没有活跃的求救信号',
            type = 'info'
        })
        return
    end

    local options = {}

    for _, rescue in ipairs(rescues) do
        local timeText = string.format('%d秒前', rescue.timeAgo)
        if rescue.timeAgo >= 60 then
            timeText = string.format('%d分钟前', math.floor(rescue.timeAgo / 60))
        end

        table.insert(options, {
            title = string.format('%s (%s)', rescue.victimName, timeText),
            description = '点击响应此求救信号',
            onSelect = function()
                -- 确认响应
                local alert = lib.alertDialog({
                    header = '响应求救信号',
                    content = '确认响应该求救信号？',
                    centered = true,
                    cancel = true
                })

                if alert == 'confirm' then
                    TriggerServerEvent('organ_trade:respondToSOS', rescue.id)
                end
            end
        })
    end

    lib.registerContext({
        id = 'active_rescues_menu',
        title = '活跃求救信号',
        options = options
    })

    lib.showContext('active_rescues_menu')
end)

-- 旧的设置救援导航点事件已删除，现在使用新的自动检测系统

-- 显示救援选项 (已删除 - 不再自动弹出选择框)
-- 医护人员需要手动使用肾上腺素道具或其他方式进行救治

-- 医护人员命令
RegisterCommand('rescues', function()
    ViewActiveRescues()
end)



-- 添加建议文本
TriggerEvent('chat:addSuggestion', '/sos', '发送求救信号')
TriggerEvent('chat:addSuggestion', '/rescues', '查看活跃求救信号（医护人员）')

-- 导出函数
exports('ViewActiveRescues', ViewActiveRescues)
exports('CanSendSOS', function() return canSendSOS end)

print('^2[器官交易系统] ^7求救系统客户端已加载')
