-- 器官交易系统主客户端文件

ESX = exports["es_extended"]:getSharedObject()

local isSystemReady = false

-- 系统初始化
CreateThread(function()
    while not ESX.IsPlayerLoaded() do
        Wait(100)
    end
    
    Wait(2000) -- 等待其他系统加载
    isSystemReady = true
    
    if Config.Debug then
        print('^2[器官交易系统] ^7客户端系统已就绪')
    end
end)

-- 道具使用处理
CreateThread(function()
    while true do
        if isSystemReady then
            -- 检查迷药道具使用
            if ESX.IsPlayerLoaded() then
                local playerData = ESX.GetPlayerData()
                
                -- 这里可以添加道具使用检测逻辑
                -- 由于ESX的道具使用通常通过服务器事件处理，这里主要用于UI交互
            end
        end
        
        Wait(1000)
    end
end)

-- 键盘快捷键
CreateThread(function()
    while true do
        if isSystemReady then
            -- F6 - 器官交易菜单（仅限有权限的玩家）
            if IsControlJustPressed(0, 167) then -- F6
                OpenOrganTradeMenu()
            end
            
            -- F7 - 医护菜单（仅限医护人员）
            if IsControlJustPressed(0, 168) then -- F7
                OpenMedicalMenu()
            end
        end
        
        Wait(0)
    end
end)

-- 打开器官交易菜单
function OpenOrganTradeMenu()
    local options = {
        {
            title = '使用迷药',
            description = '对附近玩家使用迷药',
            onSelect = function()
                exports['qiguan_tudou']:UseDrugItem()
            end
        },
        {
            title = '进行解剖',
            description = '使用手术刀进行器官提取',
            onSelect = function()
                exports['qiguan_tudou']:UseSurgeryKnife()
            end
        },
        {
            title = '查看库存',
            description = '查看当前器官库存',
            onSelect = function()
                lib.notify({
                    title = '器官交易系统',
                    description = '库存功能开发中...',
                    type = 'info'
                })
            end
        }
    }

    lib.registerContext({
        id = 'organ_trade_menu',
        title = '器官交易系统',
        options = options
    })

    lib.showContext('organ_trade_menu')
end

-- 打开医护菜单
function OpenMedicalMenu()
    local options = {
        {
            title = '使用肾上腺素',
            description = '为附近的患者注射肾上腺素延长生命',
            onSelect = function()
                -- 获取附近需要治疗的玩家
                TriggerServerEvent('organ_trade:getNearbyPatients')
            end
        },
        {
            title = '使用手术台',
            description = '使用手术台进行器官修复',
            onSelect = function()
                exports['qiguan_tudou']:UseSurgeryTable()
            end
        }
    }

    lib.registerContext({
        id = 'medical_menu',
        title = '医护系统',
        options = options
    })

    lib.showContext('medical_menu')
end

-- 状态显示
CreateThread(function()
    while true do
        if isSystemReady and Config.Debug then
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            
            -- 显示调试信息
            SetTextFont(0)
            SetTextProportional(1)
            SetTextScale(0.35, 0.35)
            SetTextColour(255, 255, 255, 255)
            SetTextDropShadow(0, 0, 0, 0, 255)
            SetTextEdge(1, 0, 0, 0, 255)
            SetTextDropShadow()
            SetTextOutline()
            SetTextEntry("STRING")
            
            local debugText = string.format(
                "器官交易系统调试信息:\n" ..
                "坐标: %.2f, %.2f, %.2f\n" ..
                "是否被迷晕: %s\n" ..
                "是否在手术中: %s\n" ..
                "可发送SOS: %s",
                coords.x, coords.y, coords.z,
                exports['qiguan_tudou']:IsDrugged() and "是" or "否",
                exports['qiguan_tudou']:IsInSurgery() and "是" or "否",
                exports['qiguan_tudou']:CanSendSOS() and "是" or "否"
            )
            
            AddTextComponentString(debugText)
            DrawText(0.01, 0.3)
        end
        
        Wait(100)
    end
end)

-- 帮助文本
CreateThread(function()
    while true do
        if isSystemReady and Config.Debug then
            local playerPed = PlayerPedId()

            -- 显示按键提示
            SetTextComponentFormat('STRING')
            AddTextComponentString('~INPUT_SELECT_CHARACTER_FRANKLIN~ 器官交易菜单  ~INPUT_SELECT_CHARACTER_MICHAEL~ 医护菜单')
            DisplayHelpTextFromStringLabel(0, 0, 1, -1)
        end

        Wait(1000)
    end
end)

-- 添加聊天建议
CreateThread(function()
    Wait(5000) -- 等待聊天系统加载
    
    TriggerEvent('chat:addSuggestion', '/sos', '发送求救信号')
    TriggerEvent('chat:addSuggestion', '/rescues', '查看活跃求救信号（医护人员）')
    TriggerEvent('chat:addSuggestion', '/surgery', '使用手术台（医护人员）')
end)

-- 处理附近患者列表
RegisterNetEvent('organ_trade:showNearbyPatients', function(patients)
    if #patients == 0 then
        lib.notify({
            title = '医护系统',
            description = '附近没有需要治疗的患者',
            type = 'info',
            duration = 3000
        })
        return
    end

    local options = {}
    for _, patient in ipairs(patients) do
        table.insert(options, {
            title = patient.name,
            description = string.format('距离: %.1fm - 使用肾上腺素延长生命', patient.distance),
            onSelect = function()
                TriggerServerEvent('organ_trade:useAdrenaline', patient.id)
            end
        })
    end

    lib.registerContext({
        id = 'nearby_patients',
        title = '附近患者',
        options = options
    })

    lib.showContext('nearby_patients')
end)

-- 导出函数
exports('OpenOrganTradeMenu', OpenOrganTradeMenu)
exports('OpenMedicalMenu', OpenMedicalMenu)
exports('IsSystemReady', function() return isSystemReady end)

print('^2[器官交易系统] ^7主客户端模块已加载')
