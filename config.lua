Config = {}

-- 基础配置
Config.Locale = 'zh'
Config.Debug = false  -- 调试模式（问题已解决，可关闭）
Config.DebugRescue = false  -- 专门的求救系统调试（可根据需要开启）

-- 迷药系统配置
Config.Drug = {
    item = 'drug_knockout',           -- 迷药道具名称
    duration = 30000,                 -- 迷晕持续时间(毫秒)
    effect_delay = 5000,              -- 药效发作延迟(毫秒)
    use_distance = 3.0               -- 使用距离
}

-- 解剖系统配置
Config.Surgery = {
    tool_item = 'surgery_knife',      -- 手术刀道具名称
    locations = {                     -- 解剖地点
        {x = 123.45, y = -678.90, z = 28.30, name = '废弃仓库'},
        {x = 234.56, y = -789.01, z = 35.40, name = '地下室'}
    },
    organs = {                        -- 器官配置
        A = {name = '心脏', item = 'organ_heart', price = 50000},
        B = {name = '肝脏', item = 'organ_liver', price = 30000},
        C = {name = '肾脏', item = 'organ_kidney', price = 25000},
        D = {name = '肺部', item = 'organ_lung', price = 20000},
        F = {name = '胰腺', item = 'organ_pancreas', price = 18000},
        G = {name = '脾脏', item = 'organ_spleen', price = 12000}
    },
    cooldown = 1000,              -- 解剖冷却时间(1小时)
    surgery_time = 10000             -- 解剖时间(毫秒) 
}

-- 求救系统配置
Config.Rescue = {
    command = 'sos',                 -- 求救命令
    response_time = 300000,          -- 医护响应时间(5分钟，毫秒)
    response_timeout = 30000,        -- 按E响应的超时时间(30秒，毫秒)
    arrival_distance = 15.0,         -- 到达距离阈值(米) - 增加到15米便于触发
    death_countdown = 300000,        -- 死亡倒计时时间(5分钟，毫秒) 【可自定义】
    allow_all_players = true,        -- 是否允许所有玩家发送求救信号（true=测试模式，false=只有手术受害者）
    jobs = {'ambulance', 'doctor', 'ems', 'medic'},  -- 可响应的职业
    blip = {
        sprite = 61,
        color = 1,
        scale = 1.0
    },
    countdown_display = {
        warning_sound_interval = 5000,   -- 最后阶段警告音间隔(毫秒) 【可自定义】
        warning_sound_threshold = 30000, -- 开始播放警告音的时间阈值(毫秒) 【可自定义】
        color_change_thresholds = {
            red_flash = 60000,           -- 红色闪烁阈值(毫秒) 【可自定义】
            orange = 120000,             -- 橙色阈值(毫秒) 【可自定义】
            -- 其他时间为黄色
        }
    }
}

-- 倒计时配置说明：
-- death_countdown: 设置死亡倒计时总时长，例如：
--   - 180000 = 3分钟
--   - 300000 = 5分钟 (默认)
--   - 600000 = 10分钟
-- warning_sound_threshold: 设置何时开始播放警告音
-- color_change_thresholds: 设置颜色变化的时间点

-- 医疗系统配置
Config.Medical = {
    adrenaline_item = 'adrenaline',  -- 肾上腺素道具
    extend_time = 600000,            -- 延长生命时间(10分钟)
    hospital_locations = {           -- 医院位置
        {x = 298.67, y = -584.23, z = 43.26, name = '中央医院'},
        {x = 1839.6, y = 3672.93, z = 34.28, name = '沙漠医院'}
    },
    surgery_table = {                -- 手术台位置
        {x = 302.45, y = -582.11, z = 43.26},
        {x = 1841.2, y = 3670.45, z = 34.28}
    },
    repair_cost = {                  -- 器官修复费用
        organ_heart = 100000,
        organ_liver = 60000,
        organ_kidney = 50000,
        organ_lung = 40000,
        organ_pancreas = 36000,
        organ_spleen = 24000
    }
}

-- 通知配置
Config.Notifications = {
    drug_used = '你使用了迷药',
    drug_effect = '药效开始发作...',
    victim_drugged = '你感到头晕目眩，身体失去控制...',
    victim_collapsed = '你倒在了地上，只能缓慢爬行...',
    crawling_hint = '按住移动键可以缓慢爬行',
    target_already_drugged = '该玩家已被迷晕，无法重复使用迷药',
    user_drugged_cannot_use = '你处于迷晕状态，无法使用迷药',
    surgery_started = '开始解剖手术',
    organ_extracted = '成功摘除%s',
    organ_removed_victim = '你的%s被摘除了，你感到身体不适...',
    sos_sent = '求救信号已发送，等待医护人员响应',
    medic_notified = '医护人员已收到求救信号',
    medic_responded = '医护人员 %s 已接受你的求救，正在赶来救援',
    rescue_responded_to_others = '该求救信号已被其他医护人员响应',
    medic_arrived = '医护人员 %s 已到达现场！',
    medic_arrived_self = '你已到达现场，可以使用肾上腺素救治受害者',
    countdown_started = '你将在%d分钟后死亡，请等待医护人员救治！', -- %d会被替换为实际分钟数
    countdown_stopped = '医护人员已为你提供治疗，生命危险解除',
    countdown_expired = '你因为没有及时得到救治而死亡',
    adrenaline_used = '使用肾上腺素延长生命',
    adrenaline_received = '医护人员给你注射了肾上腺素，你的生命得到延长',
    surgery_completed = '手术治疗完成',
    organ_repaired = '器官修复完成: %s',
    cooldown_active = '该玩家在冷却时间内，无法进行解剖',
    player_moved = '已将玩家移动到指定位置',
    surgery_ended = '手术已结束',
    surgery_ended_victim = '手术结束，你可以发送求救信号',
    continue_surgery = '可以继续提取其他器官',
    all_organs_extracted = '所有器官已提取完毕，手术结束'
}

-- 权限配置
Config.Permissions = {
    use_drug = {},                   -- 可使用迷药的职业/帮派 (空表示所有玩家)
    perform_surgery = {},            -- 可进行解剖的职业/帮派 (空表示所有玩家)
    medical_response = {'ambulance', 'doctor'} -- 医疗响应职业
}
