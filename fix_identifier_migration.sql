-- 器官交易系统标识符修复脚本
-- 此脚本用于修复char标识符和license标识符不一致的问题

-- 1. 首先备份现有数据
CREATE TABLE IF NOT EXISTS `player_organs_backup` LIKE `player_organs`;
INSERT INTO `player_organs_backup` SELECT * FROM `player_organs`;

-- 2. 查看当前数据状况
-- SELECT identifier, COUNT(*) as count FROM player_organs GROUP BY LEFT(identifier, 5) ORDER BY LEFT(identifier, 5);

-- 3. 删除char标识符的记录（这些记录是错误创建的）
-- 注意：只有在确认license标识符记录存在的情况下才删除char记录
DELETE FROM `player_organs` WHERE identifier LIKE 'char%';

-- 4. 如果需要恢复数据，可以使用以下查询
-- 但通常char记录都是默认创建的，可以安全删除

-- 5. 验证修复结果
-- SELECT identifier, organ_heart, organ_liver, organ_kidney, organ_lung, organ_pancreas, organ_spleen 
-- FROM player_organs 
-- WHERE identifier LIKE 'license%' 
-- ORDER BY identifier;

-- 6. 清理备份表（可选，建议保留一段时间）
-- DROP TABLE `player_organs_backup`;

-- 注意事项：
-- 1. 执行前请确保已备份数据库
-- 2. char标识符的记录通常是默认创建的，删除是安全的
-- 3. 真正的玩家数据应该使用license标识符
-- 4. 如果有重要的char记录需要保留，请手动检查并迁移
