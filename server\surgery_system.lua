-- 解剖系统服务器端

-- 开始解剖手术
RegisterNetEvent('organ_trade:startSurgery', function(targetId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)
    
    if not xPlayer or not xTarget then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'perform_surgery') then
        NotifyPlayer(source, '你没有权限进行解剖', 'error')
        return
    end

    -- 检查手术者是否被迷晕
    if exports['qiguan_tudou']:IsPlayerDrugged(source) then
        NotifyPlayer(source, Config.Notifications.user_drugged_cannot_use, 'error')
        return
    end

    -- 检查是否有手术刀
    if not HasPlayerItem(source, Config.Surgery.tool_item, 1) then
        NotifyPlayer(source, '你需要手术刀才能进行解剖', 'error')
        return
    end
    
    -- 检查目标是否被迷晕
    if not exports['qiguan_tudou']:IsPlayerDrugged(targetId) then
        NotifyPlayer(source, '目标必须处于迷晕状态才能进行解剖', 'error')
        return
    end
    
    -- 检查是否在指定地点
    local isNearLocation, location = IsPlayerNearLocation(source, Config.Surgery.locations, 10.0)
    if not isNearLocation then
        NotifyPlayer(source, '你必须在指定地点才能进行解剖', 'error')
        return
    end
    
    -- 检查冷却时间
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    if not CheckSurgeryCooldown(targetIdentifier) then
        NotifyPlayer(source, Config.Notifications.cooldown_active, 'error')
        return
    end
    
    -- 检查是否已经在进行手术
    if surgeryInProgress[targetIdentifier] then
        NotifyPlayer(source, '该玩家正在被解剖', 'error')
        return
    end
    
    -- 设置手术状态
    surgeryInProgress[targetIdentifier] = {
        surgeon = source,
        victim = targetId,
        location = location.name,
        startTime = GetGameTimer()
    }
    
    -- 通知双方
    NotifyPlayer(source, Config.Notifications.surgery_started, 'info')
    NotifyPlayer(targetId, '你感到身体被切开...', 'error')
    
    -- 发送器官选择界面给手术者
    TriggerClientEvent('organ_trade:openOrganSelection', source, targetId)
    
    -- 记录日志
    LogAction('SURGERY_STARTED', source, targetId, location.name)
end)

-- 提取器官
RegisterNetEvent('organ_trade:extractOrgan', function(targetId, organKey)
    local source = source
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    
    -- 验证手术状态
    if not surgeryInProgress[targetIdentifier] or surgeryInProgress[targetIdentifier].surgeon ~= source then
        NotifyPlayer(source, '无效的手术状态', 'error')
        return
    end
    
    -- 验证器官选择
    local organ = Config.Surgery.organs[organKey]
    if not organ then
        NotifyPlayer(source, '无效的器官选择', 'error')
        return
    end
    
    -- 检查玩家是否还有该器官
    local playerOrgans = GetPlayerOrgans(targetIdentifier)
    local organField = organ.item
    
    if playerOrgans[organField] == 0 then
        NotifyPlayer(source, '该器官已经被移除', 'error')
        return
    end
    
    -- 开始提取过程
    TriggerClientEvent('organ_trade:startExtraction', source, Config.Surgery.surgery_time)
    
    CreateThread(function()
        Wait(Config.Surgery.surgery_time)
        
        -- 再次验证状态
        if not surgeryInProgress[targetIdentifier] then
            return
        end
        
        -- 给手术者器官道具
        GivePlayerItem(source, organ.item, 1, {
            quality = math.random(70, 100),
            victim = GetPlayerName(targetId),
            extracted_time = os.date('%Y-%m-%d %H:%M:%S')
        })
        
        -- 更新受害者器官状态（同步等待完成）
        local updateSuccess = UpdatePlayerOrgan(targetIdentifier, organField, 0)

        -- 获取更新后的器官状态（无论更新是否成功都要获取）
        local updatedOrgans = GetPlayerOrgans(targetIdentifier)

        if updateSuccess and updatedOrgans then
            -- 验证器官状态是否正确更新
            if updatedOrgans[organField] ~= 0 then
                -- 强制设置正确的状态
                updatedOrgans[organField] = 0
            end

            -- 只发送器官状态字段，过滤掉数据库元数据
            local organStatusOnly = {
                organ_heart = updatedOrgans.organ_heart,
                organ_liver = updatedOrgans.organ_liver,
                organ_kidney = updatedOrgans.organ_kidney,
                organ_lung = updatedOrgans.organ_lung,
                organ_pancreas = updatedOrgans.organ_pancreas,
                organ_spleen = updatedOrgans.organ_spleen
            }

            TriggerClientEvent('organ_trade:refreshOrganUI', source, organStatusOnly)
        else
            -- 如果更新失败，记录错误并使用默认处理
            print('^1[器官交易系统] ^7器官状态更新失败，玩家: ' .. targetIdentifier .. ', 器官: ' .. organField)
            updatedOrgans = nil -- 确保后续检查能正确处理
        end

        -- 记录交易
        LogOrganTrade(targetIdentifier, GetPlayerIdentifierByServerId(source), organ.item, surgeryInProgress[targetIdentifier].location)

        -- 通知
        NotifyPlayer(source, string.format(Config.Notifications.organ_extracted, organ.name), 'success')
        NotifyPlayer(targetId, string.format(Config.Notifications.organ_removed_victim, organ.name), 'error')

        -- 检查是否还有器官可以提取
        local hasRemainingOrgans = false
        local organsToCheck = updatedOrgans

        -- 安全检查：确保有有效的器官数据
        if not organsToCheck or type(organsToCheck) ~= 'table' then
            -- 如果无法获取器官状态，重新获取
            organsToCheck = GetPlayerOrgans(targetIdentifier)
            print('^3[器官交易系统] ^7重新获取器官状态，玩家: ' .. targetIdentifier)
        end

        -- 检查器官状态
        if organsToCheck and type(organsToCheck) == 'table' then
            for organField, status in pairs(organsToCheck) do
                if status == 1 and organField ~= 'id' and organField ~= 'identifier' and organField ~= 'last_updated' then
                    hasRemainingOrgans = true
                    break
                end
            end
        else
            -- 如果仍然无法获取器官状态，默认结束手术
            print('^1[器官交易系统] ^7无法获取器官状态，强制结束手术，玩家: ' .. targetIdentifier)
            hasRemainingOrgans = false
        end

        -- 如果没有剩余器官，结束手术并启用求救
        if not hasRemainingOrgans then
            -- 设置冷却时间
            SetSurgeryCooldown(targetIdentifier)

            -- 清理手术状态
            surgeryInProgress[targetIdentifier] = nil

            -- 触发受害者的求救系统
            TriggerClientEvent('organ_trade:enableSOS', targetId)

            -- 通知手术完成
            NotifyPlayer(source, Config.Notifications.all_organs_extracted, 'info')
        else
            -- 还有器官可以提取，保持手术状态
            NotifyPlayer(source, Config.Notifications.continue_surgery, 'info')
        end

        -- 记录日志
        LogAction('ORGAN_EXTRACTED', source, targetId, string.format('%s (%s)', organ.name, organ.item))
    end)
end)

-- 取消手术
RegisterNetEvent('organ_trade:cancelSurgery', function(targetId)
    local source = source
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)

    if surgeryInProgress[targetIdentifier] and surgeryInProgress[targetIdentifier].surgeon == source then
        surgeryInProgress[targetIdentifier] = nil
        NotifyPlayer(source, '手术已取消', 'info')

        LogAction('SURGERY_CANCELLED', source, targetId, 'Surgery cancelled by surgeon')
    end
end)

-- 结束手术
RegisterNetEvent('organ_trade:endSurgery', function(targetId)
    local source = source
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)

    if surgeryInProgress[targetIdentifier] and surgeryInProgress[targetIdentifier].surgeon == source then
        -- 设置冷却时间
        SetSurgeryCooldown(targetIdentifier)

        -- 清理手术状态
        surgeryInProgress[targetIdentifier] = nil

        -- 触发受害者的求救系统
        TriggerClientEvent('organ_trade:enableSOS', targetId)

        -- 通知
        NotifyPlayer(source, Config.Notifications.surgery_ended, 'info')
        NotifyPlayer(targetId, Config.Notifications.surgery_ended_victim, 'info')

        LogAction('SURGERY_ENDED', source, targetId, 'Surgery ended by surgeon')
    end
end)

-- 获取附近被迷晕的玩家
RegisterNetEvent('organ_trade:getNearbyDruggedPlayers', function()
    local source = source
    local sourceCoords = GetPlayerCoords(source)
    local nearbyPlayers = {}
    
    local allPlayers = GetOnlinePlayersExcept(source)
    
    for _, player in ipairs(allPlayers) do
        -- 检查是否被迷晕
        if exports['qiguan_tudou']:IsPlayerDrugged(player.id) then
            local targetCoords = GetPlayerCoords(player.id)
            local distance = GetDistance(sourceCoords, targetCoords)
            
            if distance <= 5.0 then -- 5米范围内
                table.insert(nearbyPlayers, {
                    id = player.id,
                    name = player.name,
                    distance = math.floor(distance * 100) / 100
                })
            end
        end
    end
    
    TriggerClientEvent('organ_trade:receiveNearbyDruggedPlayers', source, nearbyPlayers)
end)

-- 检查手术状态
RegisterNetEvent('organ_trade:checkSurgeryStatus', function()
    local source = source
    local identifier = GetPlayerIdentifierByServerId(source)
    
    local isBeingSurgery = surgeryInProgress[identifier] ~= nil
    TriggerClientEvent('organ_trade:surgeryStatusResponse', source, isBeingSurgery)
end)

-- 获取玩家器官状态
RegisterNetEvent('organ_trade:getPlayerOrganStatus', function(targetId)
    local source = source

    if not HasPermission(source, 'perform_surgery') then
        return
    end

    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    local organs = GetPlayerOrgans(targetIdentifier)

    -- 只发送器官状态字段，过滤掉数据库元数据
    local organStatusOnly = {
        organ_heart = organs.organ_heart,
        organ_liver = organs.organ_liver,
        organ_kidney = organs.organ_kidney,
        organ_lung = organs.organ_lung,
        organ_pancreas = organs.organ_pancreas,
        organ_spleen = organs.organ_spleen
    }

    TriggerClientEvent('organ_trade:receiveOrganStatus', source, organStatusOnly)
end)

-- 修复器官
RegisterNetEvent('organ_trade:repairOrgan', function(targetId, organField)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)

    print(string.format('^3[修复器官] ^7收到请求 - 操作者: %d, 目标: %d, 器官: %s',
        source, targetId, organField))

    if not xPlayer or not xTarget then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        print('^1[修复器官] ^7目标玩家不存在')
        return
    end

    -- 检查权限
    if not HasPermission(source, 'perform_surgery') then
        NotifyPlayer(source, '你没有权限进行器官修复', 'error')
        print('^1[修复器官] ^7权限不足')
        return
    end

    -- 检查手术者是否被迷晕
    if exports['qiguan_tudou']:IsPlayerDrugged(source) then
        NotifyPlayer(source, Config.Notifications.user_drugged_cannot_use, 'error')
        print('^1[修复器官] ^7操作者被迷晕')
        return
    end

    -- 获取目标玩家的器官状态
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    local organs = GetPlayerOrgans(targetIdentifier)

    -- 检查器官是否已经健康
    if organs[organField] == 1 then
        NotifyPlayer(source, '该器官已经是健康状态', 'info')
        print('^3[修复器官] ^7器官已经健康')
        return
    end

    -- 检查是否有对应的修复物品
    local repairItem = Config.OrganRepair.items[organField]
    if not repairItem then
        NotifyPlayer(source, '未找到对应的修复物品配置', 'error')
        print('^1[修复器官] ^7未找到修复物品配置')
        return
    end

    -- 检查是否有修复物品
    if not HasPlayerItem(source, repairItem, 1) then
        NotifyPlayer(source, string.format('你需要 %s 来修复这个器官', repairItem), 'error')
        print(string.format('^1[修复器官] ^7缺少修复物品: %s', repairItem))
        return
    end

    -- 消耗修复物品
    RemovePlayerItem(source, repairItem, 1)

    -- 修复器官
    local success = MySQL.Sync.execute('UPDATE player_organs SET ' .. organField .. ' = 1 WHERE identifier = ?', {
        targetIdentifier
    })

    if success then
        NotifyPlayer(source, '器官修复成功！', 'success')
        NotifyPlayer(targetId, '你的器官已被修复', 'success')

        print(string.format('^2[修复器官] ^7成功修复 %s 的 %s', xTarget.getName(), organField))

        -- 记录日志
        LogAction(source, 'repair_organ', {
            target = targetId,
            target_name = xTarget.getName(),
            organ = organField,
            item_used = repairItem
        })

        -- 获取更新后的器官状态并刷新UI显示
        local updatedOrgans = GetPlayerOrgans(targetIdentifier)
        if updatedOrgans then
            -- 只发送器官状态字段，过滤掉数据库元数据
            local organStatusOnly = {
                organ_heart = updatedOrgans.organ_heart,
                organ_liver = updatedOrgans.organ_liver,
                organ_kidney = updatedOrgans.organ_kidney,
                organ_lung = updatedOrgans.organ_lung,
                organ_pancreas = updatedOrgans.organ_pancreas,
                organ_spleen = updatedOrgans.organ_spleen
            }

            TriggerClientEvent('organ_trade:refreshOrganUI', source, organStatusOnly)
        else
            -- 如果无法获取器官状态，使用旧的刷新方式
            TriggerClientEvent('organ_trade:refreshOrganStatus', source)
        end
    else
        NotifyPlayer(source, '器官修复失败', 'error')
        -- 返还物品
        GivePlayerItem(source, repairItem, 1)
        print('^1[修复器官] ^7数据库更新失败')
    end
end)

-- 导出函数
exports('IsPlayerInSurgery', function(playerId)
    local identifier = GetPlayerIdentifierByServerId(playerId)
    return surgeryInProgress[identifier] ~= nil
end)

print('^2[器官交易系统] ^7解剖系统模块已加载')
